<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Engineers</title>
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css"
      rel="stylesheet">
    <link rel="stylesheet" href="/css/engineer_style.css">
    <link rel="stylesheet" href="/css/public.css">
    <link rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Belleza&display=swap"
      rel="stylesheet">
    <link
      href="https://fonts.googleapis.com/css2?family=Dancing+Script:wght@400;500;600;700&display=swap"
      rel="stylesheet">
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css"
      rel="stylesheet">
    <link
      href="https://fonts.googleapis.com/css2?family=Great+Vibes&display=swap"
      rel="stylesheet">
    <link rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <link rel="stylesheet" href="/css/darkMode.css">
    <!-- SweetAlert2 CDN -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
  </head>

  <body>
    <style>

      .dropdown {
          position: relative;
      }

      .dropdown-menu {
          position: absolute;
          top: 100%;
          left: 0;
          background: white;
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
          border-radius: 5px;
          display: none;
          flex-direction: column;
          min-width: 150px;
      }

      .dropdown-menu a {
          display: block;
          padding: 10px;
          color: black;
          text-decoration: none;
      }

      .dropdown-menu a:hover {
          background: #f4f4f4;
      }


      .dropdown:hover .dropdown-menu {
          display: flex;
      }
      
      .logo-hero {
    background: linear-gradient(90deg, #d4af37 0%, #ffd700 50%, #bfa14a 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
    font-weight: bold;
}


.rounded-gold {
  border: 2px solid goldenrod;
  border-radius: 30px;
  padding: 8px 16px;
  outline: none;
  transition: border-color 0.3s;
}

.rounded-gold:focus {
  border-color: goldenrod;
  box-shadow: 0 0 0 0.15rem rgba(218, 165, 32, 0.25); /* Transparent gold */
}


      </style>

    <div class="container" data-aos="zoom-in" data-aos-duration="1200">
      <div class="row">
        <div
          class="navv-bar py-4 d-flex justify-content-between align-items-center">
          <div class="logo-hero d-flex align-items-center"
            style="font-size: 35px;"
            data-aos="fade-down"
            data-aos-duration="1200">
            <button class="btn btn-outline-dark ms-auto "
              onclick="history.back()">
              <i class="fa-solid fa-arrow-left"></i> Back
            </button>

            Decore&More
          </div>
          <div class="menu-btn d-lg-none" data-aos="fade-up"
            data-aos-duration="1200">
            <i class="fa-solid fa-bars"></i>
          </div>
          <div class="nav-links" data-aos="fade-up" data-aos-duration="1200">
            <ul class="list-unstyled m-0">
              <li><a href="/" class="nav-link">Home</a></li>
              <li><a href="/#op" class="nav-link">About</a></li>
              <li class="dropdown">
                <a href="/#od" class="nav-link">Services</a>
                <div class="dropdown-menu">
                  <a href="/packages/by-occasion?occasion=Birthday"
                    class="nav-link">Birthday</a>
                  <a href="/packages/by-occasion?occasion=Wedding"
                    class="nav-link">Wedding</a>
                  <a href="/packages/by-occasion?occasion=Engagement"
                    class="nav-link">Engagement</a>
                  <a href="/packages/by-occasion?occasion=BabyShower"
                    class="nav-link">BabyShower</a>
                </div>
              </li>
              <li><a href="/designers" class="nav-link">Designers</a></li>
              <% if (!user || !user.name) { %>
              <li class="dropdown">
                <a href="#" class="nav-link">Register</a>
                <div class="dropdown-menu">
                  <a href="/registerCustomer">Customer</a>
                  <a href="/register">Engineer</a>
                </div>
              </li>
              <% } %>

              <% if (!user || !user.name) { %>
              <li class="dropdown">
                <a href="#" class="nav-link">Login</a>
                <div class="dropdown-menu">
                  <a href="/login">Customer</a>
                  <a href="/login">Engineer</a>
                </div>
              </li>
              <% } %>

              <li><a href="/contact" class="nav-link">Contact</a></li>

              <% if (user && user.name) { %>
              <li class="dropdown">
                <a href="#" class="nav-link d-flex align-items-center">
                  <i class="fa-solid fa-user"></i>
                  <span><%= user.name %></span>
                </a>
                <div class="dropdown-menu">
                  <a href="/userProfile/<%= user.id %>" class="nav-link">
                    <i class="fas fa-user me-2"></i>Profile
                  </a>
                  <a href="#" class="nav-link" onclick="logout()">
                    <i class="fas fa-sign-out-alt me-2"></i>Logout
                  </a>
                </div>
              </li>
              <% }else{ %>

              <% } %>
              <button onclick="toggleDarkMode()" class="dark-mode"
                aria-label="Toggle dark mode">
                <i class="fa-solid fa-moon" aria-hidden="true"
                  title="Toggle to dark mode"></i>
              </button>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <h1 class="text-center mt-5"
      style="font-family: Dancing Script; letter-spacing: 2px; font-weight: bolder;">
      <% if (occasion) { %>
      Engineers for <%= occasion %>
      <% } else { %>
      All Engineers
      <% } %>
    </h1>

    <div class="text-center my-4">
      <label for="ratingFilter" class="fw-bold me-2">Filter by
        Rating:</label>
      <select id="ratingFilter"
        class="form-select w-auto d-inline-block rounded-gold">
        <option value="all">All</option>
        <option value="5">⭐⭐⭐⭐⭐ (5 Stars)</option>
        <option value="4">⭐⭐⭐⭐ (4 Stars & Above)</option>
        <option value="3">⭐⭐⭐ (3 Stars & Above)</option>
        <option value="2">⭐⭐ (2 Stars & Above)</option>
        <option value="1">⭐ (1 Star & Above)</option>
      </select>
      <label for="searchEngineer" class="fw-bold ms-4 me-2">Search by
        Name:</label>
      <input type="text" id="searchEngineer"
        class="form-control d-inline-block w-auto rounded-gold"
        placeholder="Enter Engineer Name">
    </div>

    <div class="container">
      <% engineers.forEach(engineer => { %>
      <div class="row engineer-card"
        data-rating="<%= Math.round(engineer.averageRating) %>">

        <div
          class="col-12 d-flex flex-column flex-md-row justify-content-evenly align-items-center">
          <div class="left col-12 col-md-6">
            <div class="m-auto mt-5" style="max-width: 300px;">
              <img
                src="<%= engineer.profilePhoto || 'https://via.placeholder.com/200'%>"
                class="img-fluid" style="border-radius: 50%;"
                alt="<%= engineer.name %>" class="profile-img">
            </div>
            <div>
              <h1 class="mt-3 text-center" style="font-size: 50px;"><%=
                engineer.firstName %> <%= engineer.lastName %></h1>
            </div>

            <p class="text-center mt-4">
              <%= engineer.bio %>
            </p>
            <div class="rating mb-2"
              style="margin-left: 280px; margin-top: 20px;">
              <% let rating = Math.round(engineer.averageRating); %>
              <% for (let i = 0; i < 5; i++) { %>
              <% if (i < rating) { %> <i
                class="bi bi-star-fill text-warning"></i> <% } else { %> <i
                class="bi bi-star"></i> <% } %>
              <% } %>
              <span class="ms-2 fw-bold"
                style="font-family: sans-serif; font-size: 18px;"><%=
                engineer.rating %></span>
            </div>

          </div>
          <div class="right col-12 col-md-6">
            <% if (engineer.hasPackages) { %>
            <h2 class="mt-5 text-center">Latest packages</h2>
            <div class="row engineer-card"
              data-rating="<%= Math.round(engineer.averageRating) %>">
              <% if (engineer.packages && engineer.packages.length > 0) { %>
              <%engineer.packages.slice(0, 3).forEach(package => { %>
              <div class="col-12 col-md-6">
                <div class="package-card p-3 mb-3 clickable-package">
                  <h5><%= package.name %></h5>
                  <p>Price:<%= package.price %>EGP</p>
                  <p>For: <%= package.eventType %></p>
                </div>
              </div>
              <% }); %>
              <% } %>
              <div class="text-center mt-3">
                <a href="/profile/<%= engineer._id %>/#packages"
                  class="btn btn-gold btn-lg rounded-pill px-4 shadow">Show
                  More</a>
              </div>
            </div>
            <% } else { %>
            <div class="text-center mt-5">
              <h2 class="mb-4">New Engineer</h2>
              <p class="lead">This engineer is new to our platform and is
                currently setting up their packages.</p>
              <a href="/profile/<%= engineer._id %>"
                class="btn btn-gold btn-lg rounded-pill px-4 shadow mt-3">
                View Profile
              </a>
            </div>
            <% } %>
          </div>
        </div>
      </div>
      <hr>
      <% }) %>
    </div>
    <style>
  .package-card {
  border: 1px solid #eee;
  border-radius: 8px;
  padding: 15px;
  background: white;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
  transition: transform 0.3s ease;
}

.package-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.btn-gold, .btn-gold:active, .btn-gold:focus, .btn-gold:visited {
  background: linear-gradient(135deg, #ffd700 0%, #d4af37 100%) !important;
  border: 2px solid #bfa14a !important;
  color: #222 !important;
  font-weight: bold !important;
  box-shadow: 0 2px 5px rgba(218, 165, 32, 0.18) !important;
  transition: background 0.3s, color 0.3s, transform 0.2s !important;
}
.btn-gold:hover, .btn-gold:focus, .btn-gold:active {
  background: linear-gradient(135deg, #ffe066 0%, #d4af37 100%) !important;
  color: #000 !important;
  transform: scale(1.05) !important;
}
</style>
    <footer class="bg-dark text-warning py-5">
      <div class="container">
        <div class="row g-4">
          <div class="col-12 text-center mb-4">
            <h2 class="display-4 fw-bold section-gradient-title">Decor&More</h2>
          </div>
          <div class="col-md-4">
            <h5 class="border-bottom border-warning pb-2">Contact Us</h5>
            <div class="mt-3">
              <p><i
                  class="bi bi-envelope-fill me-2"></i><EMAIL></p>
              <a href="https://web.whatsapp.com/send?phone=201556159175"
                target="_blank">
                <p><i class="bi bi-telephone-fill me-2"></i>+20 1556159175</p>
              </a>
              <p><i class="bi bi-geo-alt-fill me-2"></i>123 Decor Street, Design
                City</p>
            </div>
          </div>
          <div class="col-md-4">
            <h5 class="border-bottom border-warning pb-2">Quick Links</h5>
            <ul class="list-unstyled mt-3">
              <li class="mb-2"><a href="/"
                  class="text-warning text-decoration-none">Home</a></li>
              <li class="mb-2"><a href="/#op"
                  class="text-warning text-decoration-none">About</a></li>
              <% if (!user) { %>
              <li class="mb-2"><a href="/login"
                  class="text-warning text-decoration-none">login</a></li>
              <li class="mb-2"><a href="/register"
                  class="text-warning text-decoration-none">register</a></li>
              <% } %>
            </ul>
          </div>
          <div class="col-md-4">
            <h5 class="border-bottom border-warning pb-2">Follow Us</h5>
            <div class="mt-3 d-flex gap-3">
              <a href="#" class="text-warning fs-4" aria-label="Facebook"><i
                  class="bi bi-facebook"></i></a>
              <a href="#" class="text-warning fs-4" aria-label="Instagram"><i
                  class="bi bi-instagram"></i></a>
              <a href="#" class="text-warning fs-4" aria-label="Twitter"><i
                  class="bi bi-twitter-x"></i></a>
              <a href="#" class="text-warning fs-4" aria-label="LinkedIn"><i
                  class="bi bi-linkedin"></i></a>
            </div>
          </div>
          <div class="col-12">
            <hr class="border-warning">
            <p class="text-center mb-0">&copy; 2024 Decor&More. All rights
              reserved.</p>
          </div>
        </div>
      </div>
    </footer>
    <!-- Add this modal for login/register -->
    <div class="modal fade" id="authModal" tabindex="-1"
      aria-labelledby="authModalLabel" role="dialog">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="authModalLabel">Login or Register</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal"
              aria-label="Close" role="button"></button>
          </div>
          <div class="modal-body">
            <div id="loginForm" style="display: none;">
              <form id="loginFormData" class="mt-3">
                <div class="mb-3">
                  <label for="loginEmail" class="form-label">Email</label>
                  <input type="email" class="form-control" id="loginEmail"
                    required>
                </div>
                <div class="mb-3">
                  <label for="loginPassword" class="form-label">Password</label>
                  <input type="password" class="form-control" id="loginPassword"
                    required>
                </div>
                <button type="submit" class="btn btn-primary">Login</button>
              </form>
            </div>
            <div id="registerForm" style="display: none;">
              <form id="registerFormData" class="mt-3">
                <div class="mb-3">
                  <label for="registerName" class="form-label">Name</label>
                  <input type="text" class="form-control" id="registerName"
                    required>
                </div>
                <div class="mb-3">
                  <label for="registerEmail" class="form-label">Email</label>
                  <input type="email" class="form-control" id="registerEmail"
                    required>
                </div>
                <div class="mb-3">
                  <label for="registerPassword"
                    class="form-label">Password</label>
                  <input type="password" class="form-control"
                    id="registerPassword" required>
                </div>

                <div class="mb-3">
                  <label for="bio" class="form-label">Bio</label>
                  <input type="text" class="form-control" id="bio">
                </div>
                <div class="mb-3">
                  <label for="registerPhone" class="form-label">Phone</label>
                  <input type="tel" class="form-control" id="registerPhone"
                    required>
                </div>
                <div class="col-12">
                  <label class="form-label"
                    style="font-family: sans-serif;">Profile Photo</label>
                  <div class="drop-zone">
                    <span class="drop-zone__prompt"
                      style="font-family: sans-serif;">Drop file here or click
                      to upload</span>
                    <input type="file" name="profilePhoto"
                      class="drop-zone__input"
                      style="font-family: sans-serif;" accept=".jpg,.png"
                      onchange="previewProfilePhoto(event)">
                    <div class="profile-preview rounded-circle"></div>
                  </div>
                </div>
                <button type="submit" class="btn btn-primary">Register</button>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
    <script>
      function logout() {
      fetch("/logout", { method: "POST" }).then(() => (window.location.href = "/"));
    }
  </script>
    <script
      src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/js/darkMode.js"></script>
    <button class="scroll-down-btn" onclick="scrollDown()">
      <i class="fa-solid fa-arrow-down"></i>
    </button>

    <script>
      // Define login/register modal using Bootstrap 5
      var authModal = new bootstrap.Modal(document.getElementById('authModal'));
    </script>
    <script>
      function filterEngineers() {
        let selectedRating = document.getElementById("ratingFilter").value;
        let searchQuery = document.getElementById("searchEngineer").value.toLowerCase().trim();
        let engineers = document.querySelectorAll(".engineer-card");

        engineers.forEach(engineer => {
            let engineerRating = parseInt(engineer.getAttribute("data-rating"), 10);
            let nameElement = engineer.querySelector("h1");


            if (!nameElement) return;

            let engineerName = nameElement.textContent.toLowerCase();

            let matchesRating = (selectedRating === "all" || engineerRating >= parseInt(selectedRating, 10));
            let matchesSearch = engineerName.includes(searchQuery);

            if (matchesRating && matchesSearch) {
                engineer.style.display = "flex";
            } else {
                engineer.style.display = "none";
            }
        });
      }

      // Run filtering when filter changes or when typing in search
      document.getElementById("ratingFilter").addEventListener("change", filterEngineers);
      document.getElementById("searchEngineer").addEventListener("input", filterEngineers);
    </script>

  </body>
</html>